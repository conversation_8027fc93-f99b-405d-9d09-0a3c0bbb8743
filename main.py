
import streamlit as st
import asyncio
from dotenv import load_dotenv
from storyboard import generate_storyboard, generate_story_preview, ImageModel, AspectRatio, Style
from templates import PresentationTemplate, get_templates_by_industry, generate_template_preview
from brand_customization import BrandConfig, ColorPalette, AdvancedImageStyle, CustomStyleTemplate, get_style_combinations, create_brand_preview

import io
import traceback
import time
import base64
from PIL import Image

# Load environment variables
load_dotenv()

# Set page config with enhanced settings
st.set_page_config(
    page_title="AI Storyboard Generator",
    layout="wide",
    page_icon="🎬",
    initial_sidebar_state="expanded"
)

# Custom CSS for better visual design
st.markdown("""
<style>
    /* Main container styling */
    .main > div {
        padding-top: 2rem;
        padding-left: 1rem;
    }

    /* Sidebar styling */
    .css-1d391kg {
        padding-top: 2rem;
    }

    /* Enhanced title styling */
    .main h1 {
        color: #2C3E50;
        font-size: 3rem !important;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-align: center;
    }

    /* Subtitle styling */
    .main h1 + p {
        text-align: center;
        color: #7F8C8D;
        font-size: 1.2rem;
        margin-bottom: 2rem;
    }

    /* Section headers */
    .main h2, .main h3 {
        color: #34495E;
        border-bottom: 2px solid #3498DB;
        padding-bottom: 0.5rem;
        margin-top: 2rem;
    }

    /* Enhanced button styling */
    .stButton > button {
        border-radius: 8px;
        border: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .stButton > button[kind="primary"] {
        background: linear-gradient(45deg, #3498DB, #2980B9);
        color: white;
    }

    .stButton > button[kind="primary"]:hover {
        background: linear-gradient(45deg, #2980B9, #1F618D);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
    }

    /* Info boxes */
    .stInfo {
        background-color: #EBF3FD;
        border-left: 4px solid #3498DB;
    }

    .stSuccess {
        background-color: #E8F5E8;
        border-left: 4px solid #27AE60;
    }

    .stError {
        background-color: #FDEBEB;
        border-left: 4px solid #E74C3C;
    }

    /* Progress bar styling */
    .stProgress > div > div {
        background: linear-gradient(90deg, #3498DB, #2ECC71);
    }

    /* Expander styling */
    .streamlit-expanderHeader {
        background-color: #F8F9FA;
        border-radius: 8px;
        font-weight: 600;
    }

    /* Input field styling */
    .stTextInput > div > div > input,
    .stTextArea > div > div > textarea,
    .stSelectbox > div > div > select {
        border-radius: 8px;
        border: 2px solid #E1E8ED;
        transition: border-color 0.3s ease;
    }

    .stTextInput > div > div > input:focus,
    .stTextArea > div > div > textarea:focus,
    .stSelectbox > div > div > select:focus {
        border-color: #3498DB;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    /* Metric styling */
    .metric-container {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
""", unsafe_allow_html=True)

# Enhanced session state initialization
if 'generated_file' not in st.session_state:
    st.session_state.generated_file = None
if 'generation_time' not in st.session_state:
    st.session_state.generation_time = None
if 'last_error' not in st.session_state:
    st.session_state.last_error = None
if 'generation_stats' not in st.session_state:
    st.session_state.generation_stats = {}
if 'story_preview' not in st.session_state:
    st.session_state.story_preview = None

# New session state for enhanced features
if 'brand_config' not in st.session_state:
    st.session_state.brand_config = BrandConfig()
if 'custom_styles' not in st.session_state:
    st.session_state.custom_styles = []
if 'selected_template' not in st.session_state:
    st.session_state.selected_template = PresentationTemplate.PROFESSIONAL
if 'active_tab' not in st.session_state:
    st.session_state.active_tab = "Project Setup"

# Components initialization removed - unused analytics and cache_manager

# Enhanced title with subtitle
st.title("🎬 AI Storyboard Generator")
st.markdown("*Professional storyboard generation with advanced customization*")

# Create main tabs for better organization
tab1, tab2, tab3, tab4, tab5 = st.tabs([
    "� Project Setup",
    "🎨 Templates & Branding",
    "🖼️ Image Styles",
    "⚙️ Advanced Settings",
    "🚀 Generate & Preview"
])

st.session_state.active_tab = "Project Setup"  # Default tab

# Tab 1: Project Setup
with tab1:
    st.header("📝 Project Information")

    col1, col2 = st.columns([2, 1])

    with col1:
        description = st.text_area(
            "Project description",
            height=120,
            value="",
            placeholder="Describe your product, service, or customer experience you want to storyboard. Include key features, target audience, and main value proposition...",
            help="Provide a detailed description of what you're creating a storyboard for"
        )

    with col2:
        st.info("""
        **💡 Tips for better results:**

        • Be specific about your product/service
        • Mention your target audience
        • Include key features or benefits
        • Describe the main use case
        • Add any unique selling points
        """)

    expectations = st.text_area(
        "Customer insights & expectations",
        height=150,
        value="",
        placeholder="Enter each insight on a new line:\n• Customers expect fast delivery\n• Users prefer mobile-first experience\n• Price transparency is crucial\n• Support should be easily accessible",
        help="List key customer insights, expectations, or requirements that should be reflected in the journey"
    )

    # Industry selection for better context
    st.subheader("🏢 Industry Context")
    col1, col2 = st.columns(2)

    with col1:
        industry = st.selectbox(
            "Select your industry",
            options=["General", "Technology", "Healthcare", "Retail", "Finance", "Education", "Manufacturing", "Services"],
            help="This helps optimize the journey structure and terminology"
        )

    with col2:
        business_type = st.selectbox(
            "Business type",
            options=["B2C", "B2B", "B2B2C", "Non-profit", "Government"],
            help="This affects the customer journey complexity and touchpoints"
        )

# Tab 2: Templates & Branding
with tab2:
    st.header("🎨 Templates & Branding")

    # Template Selection
    st.subheader("📋 Choose Template")

    # Group templates by industry
    templates_by_industry = get_templates_by_industry()

    # Create columns for template preview
    cols = st.columns(3)
    selected_template = None

    template_options = []
    for industry, templates in templates_by_industry.items():
        st.markdown(f"**{industry}**")

        # Create a row of template previews
        template_cols = st.columns(min(len(templates), 4))

        for i, template in enumerate(templates):
            with template_cols[i % 4]:
                # Generate template preview
                preview_img = generate_template_preview(template)
                st.image(preview_img, caption=f"{template.value.icon} {template.value.name}")

                if st.button(f"Select", key=f"template_{template.name}", use_container_width=True):
                    st.session_state.selected_template = template
                    selected_template = template

                # Show template info
                with st.expander(f"ℹ️ {template.value.name}", expanded=False):
                    st.write(template.value.description)
                    st.write(f"**Industry:** {template.value.industry}")
                    st.write(f"**Font:** {template.value.font_family}")

    st.divider()

    # Brand Customization
    st.subheader("🏢 Brand Customization")

    col1, col2 = st.columns([1, 1])

    with col1:
        st.markdown("**Brand Information**")
        brand_name = st.text_input("Brand Name", value=st.session_state.brand_config.brand_name)
        st.session_state.brand_config.brand_name = brand_name

        # Logo upload
        uploaded_logo = st.file_uploader(
            "Upload Logo",
            type=['png', 'jpg', 'jpeg', 'svg'],
            help="Upload your brand logo (recommended: PNG with transparent background)"
        )

        if uploaded_logo is not None:
            # Convert uploaded file to base64
            logo_bytes = uploaded_logo.read()
            logo_b64 = base64.b64encode(logo_bytes).decode()
            st.session_state.brand_config.logo_data = logo_b64

            # Show logo preview
            st.image(uploaded_logo, caption="Logo Preview", width=150)

        # Logo positioning
        logo_position = st.selectbox(
            "Logo Position",
            options=["top-left", "top-right", "bottom-left", "bottom-right"],
            index=["top-left", "top-right", "bottom-left", "bottom-right"].index(st.session_state.brand_config.logo_position)
        )
        st.session_state.brand_config.logo_position = logo_position

    with col2:
        st.markdown("**Color Palette**")

        # Color palette selection
        palette_type = st.selectbox(
            "Palette Type",
            options=["Custom", "Predefined", "Generated from Brand Color"]
        )

        if palette_type == "Custom":
            primary_color = st.color_picker("Primary Color", value=f"#{st.session_state.brand_config.primary_color[0]:02x}{st.session_state.brand_config.primary_color[1]:02x}{st.session_state.brand_config.primary_color[2]:02x}")
            secondary_color = st.color_picker("Secondary Color", value=f"#{st.session_state.brand_config.secondary_color[0]:02x}{st.session_state.brand_config.secondary_color[1]:02x}{st.session_state.brand_config.secondary_color[2]:02x}")
            accent_color = st.color_picker("Accent Color", value=f"#{st.session_state.brand_config.accent_color[0]:02x}{st.session_state.brand_config.accent_color[1]:02x}{st.session_state.brand_config.accent_color[2]:02x}")

            # Convert hex to RGB
            st.session_state.brand_config.primary_color = tuple(int(primary_color[i:i+2], 16) for i in (1, 3, 5))
            st.session_state.brand_config.secondary_color = tuple(int(secondary_color[i:i+2], 16) for i in (1, 3, 5))
            st.session_state.brand_config.accent_color = tuple(int(accent_color[i:i+2], 16) for i in (1, 3, 5))

        elif palette_type == "Predefined":
            predefined_palettes = ColorPalette.get_predefined_palettes()
            selected_palette = st.selectbox("Choose Palette", options=list(predefined_palettes.keys()))

            if selected_palette:
                colors = predefined_palettes[selected_palette]
                st.session_state.brand_config.primary_color = colors[0]
                st.session_state.brand_config.secondary_color = colors[1]
                st.session_state.brand_config.accent_color = colors[2]

                # Show palette preview
                palette_preview = st.columns(len(colors))
                for i, color in enumerate(colors):
                    with palette_preview[i]:
                        st.markdown(f'<div style="background-color: rgb{color}; height: 50px; border-radius: 5px;"></div>', unsafe_allow_html=True)

        # Brand preview
        if st.button("🔍 Preview Brand Style"):
            brand_preview = create_brand_preview(st.session_state.brand_config)
            st.image(brand_preview, caption="Brand Style Preview")

# Tab 3: Image Styles
with tab3:
    st.header("🖼️ Image Styles & Visual Design")

    # Style Selection
    st.subheader("🎨 Choose Visual Style")

    # Basic style selection
    col1, col2 = st.columns([2, 1])

    with col1:
        # Advanced style options
        style_options = [style for style in AdvancedImageStyle]
        style_names = [style.name for style in style_options]
        style_values = [style.value for style in style_options]

        selected_style_index = st.selectbox(
            "Base Style",
            options=range(len(style_options)),
            format_func=lambda x: f"{style_names[x].replace('_', ' ').title()}",
            help="Choose the primary visual style for your storyboard images"
        )

        selected_advanced_style = style_options[selected_style_index]

        # Show style description
        st.info(f"**Style Description:** {selected_advanced_style.value}")

        # Style combinations
        st.subheader("🎭 Style Combinations")
        style_combinations = get_style_combinations()

        combination_choice = st.selectbox(
            "Pre-made Combinations",
            options=["None"] + list(style_combinations.keys()),
            help="Choose a pre-made style combination for professional results"
        )

        if combination_choice != "None":
            combo_styles = style_combinations[combination_choice]
            st.write(f"**Combination includes:** {', '.join([s.name.replace('_', ' ').title() for s in combo_styles])}")

    with col2:
        st.markdown("**Style Preview**")
        # Create a simple style preview
        style_preview_placeholder = st.empty()

        # Custom style modifiers
        st.subheader("✨ Style Modifiers")

        mood_options = st.multiselect(
            "Mood & Atmosphere",
            options=["Professional", "Friendly", "Modern", "Classic", "Energetic", "Calm", "Bold", "Subtle", "Warm", "Cool"],
            default=["Professional", "Modern"],
            help="Add mood descriptors to enhance the visual style"
        )

        technical_modifiers = st.multiselect(
            "Technical Aspects",
            options=["High Detail", "Simplified", "Clean Lines", "Textured", "Bright Colors", "Muted Tones", "High Contrast", "Soft Lighting"],
            default=["Clean Lines", "Bright Colors"],
            help="Technical aspects that affect the image generation"
        )

    # Custom Style Templates
    st.divider()
    st.subheader("💾 Custom Style Templates")

    col1, col2, col3 = st.columns(3)

    with col1:
        # Save current style as template
        template_name = st.text_input("Template Name", placeholder="My Custom Style")
        template_description = st.text_area("Description", placeholder="Describe this style template...", height=80)

        if st.button("💾 Save Style Template", use_container_width=True):
            if template_name and template_description:
                custom_template = CustomStyleTemplate(
                    name=template_name,
                    description=template_description,
                    base_style=selected_advanced_style,
                    color_palette=[st.session_state.brand_config.primary_color, st.session_state.brand_config.secondary_color],
                    style_modifiers=technical_modifiers,
                    mood_keywords=mood_options
                )
                st.session_state.custom_styles.append(custom_template)
                st.success(f"✅ Style template '{template_name}' saved!")
            else:
                st.error("Please provide both name and description")

    with col2:
        # Load saved templates
        if st.session_state.custom_styles:
            st.markdown("**Saved Templates**")
            for i, template in enumerate(st.session_state.custom_styles):
                if st.button(f"📋 {template.name}", key=f"load_template_{i}", use_container_width=True):
                    # Load template settings
                    st.success(f"Loaded template: {template.name}")
                    # Here you would apply the template settings

    with col3:
        # Template management
        if st.session_state.custom_styles:
            st.markdown("**Manage Templates**")
            template_to_delete = st.selectbox(
                "Delete Template",
                options=["None"] + [t.name for t in st.session_state.custom_styles]
            )

            if template_to_delete != "None" and st.button("🗑️ Delete", use_container_width=True):
                st.session_state.custom_styles = [t for t in st.session_state.custom_styles if t.name != template_to_delete]
                st.success(f"Deleted template: {template_to_delete}")
                st.rerun()

# Tab 4: Advanced Settings
with tab4:
    st.header("⚙️ Advanced Settings")

    # Technical Settings
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📸 Image Settings")
        image_model = st.selectbox(
            "Image model",
            options=[model.name for model in ImageModel],
            index=list(ImageModel).index(ImageModel.OPENAI_DALLE_3),
            format_func=lambda x: ImageModel[x].value,
            help="AI model used for image generation"
        )

        aspect_ratio = st.selectbox(
            "Image aspect ratio",
            options=[ratio.name for ratio in AspectRatio],
            index=list(AspectRatio).index(AspectRatio.WIDESCREEN),
            format_func=lambda x: AspectRatio[x].value,
            help="Aspect ratio for all generated images"
        )

        # Journey Settings
        st.subheader("📊 Journey Settings")
        nb_steps = st.number_input(
            "Number of steps",
            min_value=0,
            max_value=20,
            step=1,
            value=3,
            help="Total number of steps across all journey stages (0 = let AI decide optimal number)"
        )

        # Convert 0 to None for the backend
        if nb_steps == 0:
            nb_steps = None

    with col2:
        # Content Options
        st.subheader("📝 Content Options")
        include_emotion = st.checkbox(
            "Include Emotions",
            value=True,
            help="Include customer emotions in the journey steps and descriptions"
        )

        include_challenges = st.checkbox(
            "Include Challenges",
            value=True,
            help="Include pain points and challenges in the customer journey"
        )

        if not include_emotion and not include_challenges:
            st.warning("⚠️ Both emotions and challenges are disabled. The journey will focus only on functional aspects.")
        elif not include_emotion:
            st.info("💡 Emotions disabled: Journey will focus on actions and outcomes.")
        elif not include_challenges:
            st.info("💡 Challenges disabled: Journey will show a smooth, positive experience.")

        # Language Settings
        st.subheader("🌍 Language Settings")
        output_language = st.selectbox(
            "Output Language",
            options=[
                "English", "Spanish", "French", "German", "Italian", "Portuguese",
                "Dutch", "Russian", "Chinese (Simplified)", "Chinese (Traditional)",
                "Japanese", "Korean", "Arabic", "Hindi", "Turkish", "Polish",
                "Swedish", "Norwegian", "Danish", "Finnish"
            ],
            index=0,
            help="Language for all generated content including story, descriptions, and persona details"
        )

    # Terms to Avoid Section
    st.divider()
    st.subheader("🚫 Terms to Avoid")

    col1, col2 = st.columns([2, 1])

    with col1:
        new_term = st.text_input(
            "Add term to avoid",
            key="new_term_input",
            placeholder="e.g., 'complicated', 'expensive', 'slow'",
            help="Add words or phrases that should not appear in the generated content"
        )

        if st.button("➕ Add term", type="secondary"):
            if new_term and new_term.strip():  # Check for non-empty string
                if 'avoided_terms' not in st.session_state:
                    st.session_state.avoided_terms = []
                term_to_add = new_term.strip().lower()
                if term_to_add not in [t.lower() for t in st.session_state.avoided_terms]:
                    st.session_state.avoided_terms.append(new_term.strip())
                    st.rerun()

        # Initialize avoided terms if not exists
        if 'avoided_terms' not in st.session_state:
            st.session_state.avoided_terms = []

        # Display and manage avoided terms
        if st.session_state.avoided_terms:
            avoided_terms = st.multiselect(
                "Selected terms to avoid (click to remove)",
                options=st.session_state.avoided_terms,
                default=st.session_state.avoided_terms,
                help="These terms will be avoided in all generated content"
            )

            # Update session state if terms were removed
            if len(avoided_terms) != len(st.session_state.avoided_terms):
                st.session_state.avoided_terms = avoided_terms
        else:
            avoided_terms = []

    with col2:
        if st.session_state.avoided_terms:
            if st.button("🗑️ Clear all terms", type="secondary", use_container_width=True):
                st.session_state.avoided_terms = []
                st.rerun()
        else:
            st.info("💡 Add terms you want to avoid in the generated story and descriptions")

    # Settings Preview
    st.divider()
    st.subheader("ℹ️ Current Settings Summary")

    # Map advanced style back to basic style for compatibility
    style_mapping = {
        "SKETCHY_BW_GRAPHIC": Style.SKETCHY_BW_GRAPHIC,
        "CARTOON": Style.CARTOON,
        "REALISTIC": Style.REALISTIC,
        "CARTOON_PROFESSIONAL": Style.CARTOON,
        "REALISTIC_ARTISTIC": Style.REALISTIC,
        "SKETCH_COLORFUL": Style.SKETCHY_BW_GRAPHIC,
        "MINIMAL_ELEGANT": Style.SKETCHY_BW_GRAPHIC,
    }

    # Get the current advanced style selection
    if 'selected_advanced_style' in locals():
        mapped_style = style_mapping.get(selected_advanced_style.name, Style.SKETCHY_BW_GRAPHIC)
        style_display = selected_advanced_style.value
    else:
        mapped_style = Style.SKETCHY_BW_GRAPHIC
        style_display = Style.SKETCHY_BW_GRAPHIC.value

    st.info(f"""
    **Configuration:**
    - **Model:** {ImageModel[image_model].value}
    - **Aspect:** {AspectRatio[aspect_ratio].value}
    - **Style:** {style_display}
    - **Steps:** {nb_steps if nb_steps else 'AI-determined'}
    - **Language:** {output_language}
    - **Emotions:** {'✅' if include_emotion else '❌'}
    - **Challenges:** {'✅' if include_challenges else '❌'}
    - **Brand:** {st.session_state.brand_config.brand_name or 'Default'}
    - **Template:** {st.session_state.selected_template.value.name}
    """)






# Tab 5: Generate & Preview
with tab5:
    st.header("🚀 Generate & Preview")

    # Quick summary of current configuration
    st.subheader("� Configuration Summary")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("Template", st.session_state.selected_template.value.name)
        st.metric("Language", output_language)

    with col2:
        st.metric("Image Model", ImageModel[image_model].value)
        st.metric("Aspect Ratio", AspectRatio[aspect_ratio].value)

    with col3:
        st.metric("Journey Steps", nb_steps if nb_steps else "AI-determined")
        st.metric("Brand", st.session_state.brand_config.brand_name or "Default")

    # Get avoided terms from session state
    avoided_terms = st.session_state.get('avoided_terms', [])

    # Map advanced style back to basic style for backend compatibility
    style_mapping = {
        "SKETCHY_BW_GRAPHIC": Style.SKETCHY_BW_GRAPHIC,
        "CARTOON": Style.CARTOON,
        "REALISTIC": Style.REALISTIC,
        "CARTOON_PROFESSIONAL": Style.CARTOON,
        "REALISTIC_ARTISTIC": Style.REALISTIC,
        "SKETCH_COLORFUL": Style.SKETCHY_BW_GRAPHIC,
        "MINIMAL_ELEGANT": Style.SKETCHY_BW_GRAPHIC,
        "WATERCOLOR": Style.CUSTOM,
        "OIL_PAINTING": Style.CUSTOM,
        "DIGITAL_ART": Style.CUSTOM,
        "PENCIL_SKETCH": Style.SKETCHY_BW_GRAPHIC,
        "INK_DRAWING": Style.SKETCHY_BW_GRAPHIC,
        "PASTEL": Style.CUSTOM,
        "VECTOR_ART": Style.CUSTOM,
        "MINIMALIST": Style.SKETCHY_BW_GRAPHIC,
        "VINTAGE": Style.CUSTOM,
        "ISOMETRIC": Style.CUSTOM,
    }

    # Get the current advanced style selection and map it
    if 'selected_advanced_style' in locals():
        style = style_mapping.get(selected_advanced_style.name, Style.SKETCHY_BW_GRAPHIC)
        if style == Style.CUSTOM:
            style_description = selected_advanced_style.value
        else:
            style_description = ""
    else:
        style = Style.SKETCHY_BW_GRAPHIC
        style_description = ""



    # Validation checks
    validation_errors = []
    if not description.strip():
        validation_errors.append("Project description is required")
    if nb_steps is not None and nb_steps < 3:
        validation_errors.append("Minimum 3 steps required for a meaningful journey (or set to 0 for AI to decide)")
    if style == Style.CUSTOM and not style_description.strip():
        validation_errors.append("Custom style description is required when using custom style")

# Display validation errors
if validation_errors:
    for error in validation_errors:
        st.error(f"❌ {error}")

col1, col2, col3, col4 = st.columns([2, 1, 1, 1])

with col1:
    generate_disabled = len(validation_errors) > 0

    # Show different button text based on whether we have a preview
    if st.session_state.story_preview and not st.session_state.story_preview.get('error'):
        button_text = "🎬 Generate Storyboard from Preview"
        button_help = "Generate the full storyboard using your edited preview"
    else:
        button_text = "🎬 Generate Storyboard"
        button_help = "Generate a new storyboard from scratch"

    if st.button(button_text, type="primary", disabled=generate_disabled, help=button_help):
        start_time = time.time()

        # Clear previous errors
        st.session_state.last_error = None

        # Enhanced progress tracking
        progress_bar = st.progress(0)
        status_placeholder = st.empty()
        detailed_progress = st.empty()

        def set_progress(message):
            status_placeholder.info(f"🔄 {message}")

            # Update progress bar based on message
            if "Story generation" in message:
                progress_bar.progress(0.1)
            elif "Persona generation" in message:
                progress_bar.progress(0.2)
            elif "Scenes generation" in message:
                if "/" in message:
                    try:
                        current, total = message.split("...")[-1].strip().split("/")
                        current = int(current)
                        total = int(total)
                        scene_progress = 0.3 + (0.6 * current / total)
                        progress_bar.progress(min(scene_progress, 0.9))
                    except:
                        progress_bar.progress(0.5)
                else:
                    progress_bar.progress(0.3)

        try:
            # Process expectations
            expectations_list = [exp.strip() for exp in expectations.split("\n") if exp.strip()] if expectations else []

            # Check if we have an edited preview to use
            edited_story_data = None
            if st.session_state.story_preview and not st.session_state.story_preview.get('error'):
                edited_story_data = st.session_state.story_preview
                st.info("🎯 Using your edited story preview for generation!")

            # Generate storyboard
            prs = asyncio.run(generate_storyboard(
                set_progress,
                description.strip(),
                expectations_list,
                ImageModel[image_model],
                nb_steps,
                AspectRatio[aspect_ratio],
                style,  # style is already a Style enum object
                style_description.strip() if style_description else "",
                avoided_terms,
                include_emotion,
                include_challenges,
                output_language,
                edited_story_data  # Pass the edited story data
            ))

            # Save to buffer
            buffer = io.BytesIO()
            prs.save(buffer)
            buffer.seek(0)
            st.session_state.generated_file = buffer.getvalue()

            # Calculate generation time
            generation_time = time.time() - start_time
            st.session_state.generation_time = generation_time

            # Store generation stats
            st.session_state.generation_stats = {
                'steps': nb_steps if nb_steps is not None else "AI-determined",
                'style': style.value,  # style is already a Style enum object
                'aspect_ratio': AspectRatio[aspect_ratio].value,
                'expectations_count': len(expectations_list),
                'avoided_terms_count': len(avoided_terms),
                'generation_time': generation_time,
                'include_emotion': include_emotion,
                'include_challenges': include_challenges,
                'output_language': output_language
            }

            progress_bar.progress(1.0)
            status_placeholder.success(f"✅ Generation completed in {generation_time:.1f} seconds!")
            detailed_progress.empty()

        except Exception as e:
            error_msg = str(e)
            st.session_state.last_error = error_msg

            progress_bar.empty()
            status_placeholder.error(f"❌ Generation failed: {error_msg}")

            # Show detailed error in expander
            with st.expander("🔍 Error Details", expanded=False):
                st.code(traceback.format_exc())
                st.info("💡 Try adjusting your settings or simplifying your description")

with col2:
    preview_disabled = len(validation_errors) > 0

    if st.button("👁️ Preview Story", type="secondary", disabled=preview_disabled, help="Generate a preview that you can edit before creating the full storyboard"):
        with st.spinner("Generating story preview..."):
            try:
                expectations_list = [exp.strip() for exp in expectations.split("\n") if exp.strip()] if expectations else []

                preview_data = generate_story_preview(
                    description.strip(),
                    expectations_list,
                    nb_steps,
                    avoided_terms,
                    include_emotion,
                    include_challenges,
                    output_language
                )

                st.session_state.story_preview = preview_data
                st.success("✅ Story preview generated! Scroll down to view and edit it.")

            except Exception as e:
                st.error(f"❌ Preview failed: {str(e)}")

with col3:
    if st.button("🔄 Reset All", type="secondary"):
        # Clear all session state
        keys_to_keep = []  # Keep nothing, full reset
        for key in list(st.session_state.keys()):
            if key not in keys_to_keep:
                del st.session_state[key]
        st.rerun()


# Story Preview Section with Editing Capabilities
if st.session_state.story_preview and not st.session_state.story_preview.get('error'):
    st.markdown("---")
    st.subheader("👁️ Story Preview & Editor")

    preview = st.session_state.story_preview

    # Edit mode controls
    col1, col2, col3, col4 = st.columns([2, 1, 1, 1])
    with col1:
        edit_mode = st.toggle("✏️ Edit Mode", value=False, help="Enable editing of the story preview")

    with col2:
        if st.button("🔄 Regenerate", help="Generate a new preview with current settings"):
            with st.spinner("Regenerating story preview..."):
                try:
                    expectations_list = [exp.strip() for exp in expectations.split("\n") if exp.strip()] if expectations else []

                    new_preview_data = generate_story_preview(
                        description.strip(),
                        expectations_list,
                        nb_steps,
                        avoided_terms,
                        include_emotion,
                        include_challenges,
                        output_language
                    )

                    st.session_state.story_preview = new_preview_data
                    st.rerun()
                except Exception as e:
                    st.error(f"❌ Regeneration failed: {str(e)}")

    with col3:
        if edit_mode and st.button("💾 Save Changes", type="primary", help="Save your edits"):
            st.success("✅ Changes saved!")
            # Force a rerun to update the preview data
            st.rerun()

    with col4:
        if st.button("🗑️ Clear Preview", help="Clear the preview to start over"):
            st.session_state.story_preview = None
            st.rerun()

    # Story overview editing
    col1, col2 = st.columns([2, 1])

    with col1:
        if edit_mode:
            story_title = st.text_input(
                "📖 Story Title",
                value=preview['story_name'],
                key="edit_story_title"
            )
            preview['story_name'] = story_title

            story_context = st.text_area(
                "Story Context",
                value=preview['story_context'],
                height=100,
                key="edit_story_context"
            )
            preview['story_context'] = story_context
        else:
            st.markdown(f"### 📖 {preview['story_name']}")
            st.markdown(f"**Story Context:** {preview['story_context']}")

    with col2:
        st.markdown("### 👤 Persona")
        persona = preview['persona']

        if edit_mode:
            persona_name = st.text_input(
                "Persona Name",
                value=persona['name'],
                key="edit_persona_name"
            )
            persona['name'] = persona_name

            persona_age = st.text_input(
                "Age Range",
                value=persona['age_range'],
                key="edit_persona_age"
            )
            persona['age_range'] = persona_age

            persona_occupation = st.text_input(
                "Occupation",
                value=persona['occupation'],
                key="edit_persona_occupation"
            )
            persona['occupation'] = persona_occupation

            persona_traits = st.text_area(
                "Key Traits (comma-separated)",
                value=', '.join(persona['traits']),
                height=60,
                key="edit_persona_traits"
            )
            persona['traits'] = [trait.strip() for trait in persona_traits.split(',') if trait.strip()]
        else:
            st.markdown(f"""
            **{persona['name']}** | {persona['age_range']} | {persona['occupation']}

            **Key traits:** {', '.join(persona['traits'])}
            """)

    # Journey stages editing
    st.markdown("### 🗺️ Customer Journey")

    for i, stage in enumerate(preview['stages']):
        with st.expander(f"**Stage {i+1}: {stage['name']}**", expanded=i==0):

            if edit_mode:
                # Stage editing
                col1, col2 = st.columns(2)
                with col1:
                    stage_name = st.text_input(
                        "Stage Name",
                        value=stage['name'],
                        key=f"edit_stage_name_{i}"
                    )
                    stage['name'] = stage_name

                with col2:
                    stage_goal = st.text_input(
                        "Stage Goal",
                        value=stage['goal'],
                        key=f"edit_stage_goal_{i}"
                    )
                    stage['goal'] = stage_goal

                stage_description = st.text_area(
                    "Stage Description",
                    value=stage['description'],
                    height=80,
                    key=f"edit_stage_desc_{i}"
                )
                stage['description'] = stage_description
            else:
                st.markdown(f"**Goal:** {stage['goal']}")
                st.markdown(f"**Description:** {stage['description']}")

            st.markdown("**Steps:**")
            for j, step in enumerate(stage['steps']):
                if edit_mode:
                    with st.container():
                        st.markdown(f"**Step {j+1}:**")

                        # Step editing
                        step_name = st.text_input(
                            "Step Name",
                            value=step['name'],
                            key=f"edit_step_name_{i}_{j}"
                        )
                        step['name'] = step_name

                        step_description = st.text_area(
                            "Step Description",
                            value=step['description'],
                            height=80,
                            key=f"edit_step_desc_{i}_{j}"
                        )
                        step['description'] = step_description

                        # Emotion editing (if enabled)
                        if include_emotion and step['emotion'] != 'N/A':
                            step_emotion = st.text_input(
                                "Customer Emotion",
                                value=step['emotion'],
                                key=f"edit_step_emotion_{i}_{j}"
                            )
                            step['emotion'] = step_emotion

                        # Visual style selection
                        shot_types = ["Full shot", "American shot", "Medium shot", "Medium close-up shot", "Close-up shot"]
                        current_shot = step.get('shot_type', 'Medium shot')
                        shot_index = shot_types.index(current_shot) if current_shot in shot_types else 0

                        step_shot = st.selectbox(
                            "Visual Style",
                            options=shot_types,
                            index=shot_index,
                            key=f"edit_step_shot_{i}_{j}"
                        )
                        step['shot_type'] = step_shot

                        st.divider()
                else:
                    emotion_line = f"- *Customer emotion:* {step['emotion']}" if include_emotion and step['emotion'] != 'N/A' else ""
                    st.markdown(f"""
                    **{j+1}. {step['name']}**
                    - *Description:* {step['description']}
                    {emotion_line}
                    - *Visual style:* {step['shot_type']}
                    """)

    # Action buttons for preview
    if edit_mode:
        st.info("💡 **Edit Mode Active:** Make changes above, then click 'Save Changes' to update the preview.")
    else:
        col1, col2 = st.columns([1, 1])
        with col1:
            if st.button("✅ Generate full storyboard from this preview", type="primary"):
                # Keep the preview so it can be used for generation
                st.success("🎯 Preview will be used for storyboard generation! Click 'Generate Storyboard' above.")
                st.balloons()

        with col2:
            if st.button("✏️ Modify settings", type="secondary"):
                st.session_state.story_preview = None  # Clear preview
                st.info("💡 Adjust your settings above and try the preview again")

elif st.session_state.story_preview and st.session_state.story_preview.get('error'):
    st.error(f"Preview Error: {st.session_state.story_preview['error']}")


# Enhanced download and results section
if st.session_state.generated_file:
    st.markdown("---")
    st.subheader("📥 Download Results")

    col1, col2 = st.columns([2, 1])

    with col1:
        # Generate filename with timestamp
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"storyboard_{timestamp}.pptx"

        st.download_button(
            label="📥 Download Storyboard (PowerPoint)",
            data=st.session_state.generated_file,
            file_name=filename,
            mime="application/vnd.openxmlformats-officedocument.presentationml.presentation",
            type="primary"
        )

        # Show generation stats if available
        if st.session_state.generation_stats:
            stats = st.session_state.generation_stats
            st.success(f"""
            ✅ **Generation Summary:**
            - **Steps:** {stats['steps']} journey steps
            - **Style:** {stats['style']}
            - **Format:** {stats['aspect_ratio']}
            - **Language:** {stats.get('output_language', 'English')}
            - **Emotions:** {'✅ Included' if stats.get('include_emotion', True) else '❌ Excluded'}
            - **Challenges:** {'✅ Included' if stats.get('include_challenges', True) else '❌ Excluded'}
            - **Generation time:** {stats['generation_time']:.1f} seconds
            - **Insights used:** {stats['expectations_count']}
            - **Terms avoided:** {stats['avoided_terms_count']}
            """)

    with col2:
        # Dynamic "What's included" based on settings
        if st.session_state.generation_stats:
            stats = st.session_state.generation_stats
            emotion_included = stats.get('include_emotion', True)
            challenges_included = stats.get('include_challenges', True)
            language = stats.get('output_language', 'English')

            included_items = [
                "✅ Complete customer journey",
                "✅ AI-generated persona",
                "✅ Professional layout",
                "✅ Custom visuals for each step",
                "✅ Detailed step descriptions",
                f"✅ Content in {language}",
            ]

            if emotion_included:
                included_items.append("✅ Emotional journey mapping")

            if challenges_included:
                included_items.append("✅ Challenge and pain point analysis")

            st.info("**📋 What's included:**\n\n" + "\n".join(included_items))
        else:
            st.info("""
            **📋 What's included:**

            ✅ Complete customer journey
            ✅ AI-generated persona
            ✅ Professional layout
            ✅ Custom visuals for each step
            ✅ Detailed step descriptions
            ✅ Customizable content options
            """)

# Enhanced status section
st.markdown("---")
status_col1, status_col2 = st.columns([1, 1])

with status_col1:
    if st.session_state.generated_file:
        st.success("🎉 Storyboard ready for download!")
    elif st.session_state.last_error:
        st.error("❌ Last generation failed")
    else:
        st.info("⏳ Ready to generate your storyboard")

with status_col2:
    if st.session_state.generation_time:
        st.metric("⏱️ Last Generation Time", f"{st.session_state.generation_time:.1f}s")
    else:
        st.metric("📊 Status", "Idle")



st.markdown("""
<div style='text-align: center; color: #7F8C8D; margin-top: 2rem;'>
    <small>🤖 Powered by AI • Enhanced for Professional Storyboarding</small>
</div>
""", unsafe_allow_html=True)

"""
Template configurations for different storyboard styles and use cases
"""

from enum import Enum
from dataclasses import dataclass
from typing import Dict, <PERSON><PERSON>

@dataclass
class TemplateConfig:
    name: str
    primary_color: Tuple[int, int, int]
    secondary_color: Tuple[int, int, int]
    background_color: Tuple[int, int, int]
    font_family: str
    layout_style: str = "corporate"
    accent_color: Tuple[int, int, int] = (52, 152, 219)
    text_color: <PERSON><PERSON>[int, int, int] = (44, 62, 80)
    description: str = ""

class PresentationTemplate(Enum):
    PROFESSIONAL = TemplateConfig(
        name="Professional Business",
        primary_color=(44, 62, 80),
        secondary_color=(52, 152, 219),
        background_color=(248, 249, 250),
        font_family="Arial",
        layout_style="corporate",
        accent_color=(52, 152, 219),
        text_color=(44, 62, 80),
        description="Clean, professional design perfect for business presentations"
    )
    CREATIVE = TemplateConfig(
        name="Creative & Colorful",
        primary_color=(155, 89, 182),
        secondary_color=(241, 196, 15),
        background_color=(253, 254, 254),
        font_family="Calibri",
        layout_style="modern",
        accent_color=(241, 196, 15),
        text_color=(44, 62, 80),
        description="Vibrant and engaging design for creative projects"
    )
    MINIMAL = TemplateConfig(
        name="Minimal & Clean",
        primary_color=(45, 52, 54),
        secondary_color=(99, 110, 114),
        background_color=(255, 255, 255),
        font_family="Helvetica",
        layout_style="clean",
        accent_color=(46, 204, 113),
        text_color=(44, 62, 80),
        description="Simple, clean design focusing on content over decoration"
    )
    TECH = TemplateConfig(
        name="Tech & Modern",
        primary_color=(0, 123, 255),
        secondary_color=(40, 167, 69),
        background_color=(248, 249, 250),
        font_family="Segoe UI",
        layout_style="tech",
        accent_color=(26, 188, 156),
        text_color=(44, 62, 80),
        description="Modern, tech-inspired design with clean lines"
    )

def get_template_config(template: PresentationTemplate) -> TemplateConfig:
    """Get configuration for a specific template"""
    return template.value

def get_available_templates() -> Dict[str, str]:
    """Get list of available templates with descriptions"""
    return {template.name: template.value.description for template in PresentationTemplate}

# Industry-specific prompt enhancements
INDUSTRY_PROMPTS = {
    "E-commerce": {
        "context_enhancement": "Focus on online shopping experience, product discovery, cart management, and delivery tracking",
        "key_stages": ["Discovery", "Product Research", "Purchase Decision", "Checkout", "Delivery", "Post-Purchase"],
        "common_emotions": ["curious", "excited", "hesitant", "confident", "satisfied", "delighted"]
    },
    
    "SaaS/Software": {
        "context_enhancement": "Emphasize user onboarding, feature adoption, problem-solving, and value realization",
        "key_stages": ["Problem Recognition", "Solution Research", "Trial/Demo", "Onboarding", "Feature Adoption", "Renewal"],
        "common_emotions": ["frustrated", "hopeful", "confused", "accomplished", "productive", "loyal"]
    },
    
    "Healthcare": {
        "context_enhancement": "Focus on patient care, appointment scheduling, treatment journey, and follow-up care",
        "key_stages": ["Symptom Recognition", "Provider Search", "Appointment", "Consultation", "Treatment", "Recovery"],
        "common_emotions": ["concerned", "anxious", "hopeful", "relieved", "recovering", "grateful"]
    },
    
    "Financial Services": {
        "context_enhancement": "Emphasize trust, security, financial goals, and long-term relationships",
        "key_stages": ["Financial Need", "Research", "Consultation", "Decision", "Implementation", "Monitoring"],
        "common_emotions": ["worried", "cautious", "informed", "confident", "secure", "empowered"]
    },
    
    "Education": {
        "context_enhancement": "Focus on learning journey, skill development, progress tracking, and achievement",
        "key_stages": ["Learning Need", "Course Discovery", "Enrollment", "Learning", "Assessment", "Certification"],
        "common_emotions": ["curious", "motivated", "challenged", "accomplished", "proud", "inspired"]
    }
}

def get_industry_enhancement(industry: str) -> Dict[str, any]:
    """Get industry-specific enhancements for story generation"""
    return INDUSTRY_PROMPTS.get(industry, {
        "context_enhancement": "Focus on user needs, experience quality, and value delivery",
        "key_stages": ["Awareness", "Consideration", "Decision", "Onboarding", "Usage", "Advocacy"],
        "common_emotions": ["interested", "evaluating", "deciding", "learning", "satisfied", "loyal"]
    })
